package common

import "time"

const (
	ConstLockExpireTime        = 5 * time.Second
	ConstDefaultKeepNumber     = 10
	ConstDefaultKeepDuration   = 72 * time.Hour
	ConstDefaultMaxNumber      = 100
	ConstDefaultMaxDeleteItems = 500

	ConstRedisRecord          = "reporter:record:"
	ConstRedisSuiteRecord     = "reporter:suite_record:"
	ConstRedisInterfaceRecord = "reporter:interface_record:"
	ConstRedisPlanRecord      = "reporter:plan_record:"
	ConstRedisServiceRecord   = "reporter:service_record:"

	ConstLockUIDevicePerfDataTaskIDExecuteIDProjectIDUDIDDataTypePrefix        = "lock:reporter:uiDevicePerfData:taskID:executeID:projectID:udid:dataType"        // `ui_device_perf_data
	ConstLockStabilityDevicePerfDataTaskIDExecuteIDProjectIDUDIDDataTypePrefix = "lock:reporter:stabilityDevicePerfData:taskID:executeID:projectID:udid:dataType" // `stability_device_perf_data
	ConstLockPerfCaseRecordTaskIDExecuteIDProjectIDPrefix                      = "lock:reporter:perfCase:taskID:executeID:projectID"                              // `perf_case_execution_record`
	ConstLockPerfSuiteRecordTaskIDExecuteIDProjectIDPrefix                     = "lock:reporter:perfSuite:taskID:executeID:projectID"                             // `perf_suite_execution_record`
	ConstLockPerfPlanRecordTaskIDExecuteIDProjectIDPrefix                      = "lock:reporter:perfPlan:taskID:executeID:projectID"                              // `perf_plan_execution_record`
	ConstLockStabilityPlanRecordTaskIDExecuteIDProjectIDPrefix                 = "lock:reporter:stabilityPlan:taskID:executeID:projectID"                         // `stability_plan_execution_record`
	ConstLockStabilityDeviceRecordTaskIDExecuteIDProjectIDPrefix               = "lock:reporter:stabilityDevice:taskID:executeID:projectID"                       // `stability_plan_execution_record`
	ConstLockStabilityDeviceStepTaskIDExecuteIDProjectIDStageIndexPrefix       = "lock:reporter:stabilityDeviceStep:taskID:executeID:projectID:stage:index"       // `stability_device_step`
	ConstLockUIAgentComponentRecordTaskIDExecuteIDProjectIDPrefix              = "lock:reporter:uiAgentComponent:taskID:executeID:projectID"                      // `ui_agent_component_execution_record`
)

// RecordCleanFlag 执行记录清理标识
type RecordCleanFlag = int8

const (
	NotCleaned RecordCleanFlag = iota // 0: 未清理
	HasCleaned                        // 1: 已清理
)

type ReqFieldKey = string

const (
	ReqFieldKeyTaskID             ReqFieldKey = "task_id"
	ReqFieldKeyExecuteID          ReqFieldKey = "execute_id"
	ReqFieldKeyExecuteType        ReqFieldKey = "execute_type"
	ReqFieldKeyProjectID          ReqFieldKey = "project_id"
	ReqFieldKeyPlanID             ReqFieldKey = "plan_id"
	ReqFieldKeyPlanExecuteID      ReqFieldKey = "plan_execute_id"
	ReqFieldKeySuiteID            ReqFieldKey = "suite_id"
	ReqFieldKeySuiteExecuteID     ReqFieldKey = "suite_execute_id"
	ReqFieldKeyInterfaceID        ReqFieldKey = "interface_id"
	ReqFieldKeyInterfaceExecuteID ReqFieldKey = "interface_execute_id"
	ReqFieldKeyCaseID             ReqFieldKey = "case_id"
	ReqFieldKeyComponentID        ReqFieldKey = "component_id"
	ReqFieldKeyComponentType      ReqFieldKey = "component_type"
)

type RecordStatus = string

const (
	Success RecordStatus = "Success"
	Skip    RecordStatus = "Skip"
	Failure RecordStatus = "Failure"
	Panic   RecordStatus = "Panic"
	Stop    RecordStatus = "Stop"
	Invalid RecordStatus = "Invalid"
)
