package model

import (
	"context"

	"github.com/Masterminds/squirrel"

	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

var (
	_ UiCaseExecutionStepModel = (*customUiCaseExecutionStepModel)(nil)

	uiCaseExecutionStepInsertFields = stringx.Remove(
		uiCaseExecutionStepFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`",
		"`updated_at`", "`deleted_at`",
	)
)

type (
	// UiCaseExecutionStepModel is an interface to be customized, add more methods here,
	// and implement the added methods in customUiCaseExecutionStepModel.
	UiCaseExecutionStepModel interface {
		uiCaseExecutionStepModel
		types.DBModel

		withSession(session sqlx.Session) UiCaseExecutionStepModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *UiCaseExecutionStep) squirrel.InsertBuilder
		UpdateBuilder(data *UiCaseExecutionStep) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*UiCaseExecutionStep, error)

		FindByTaskIDExecuteIDProjectID(
			ctx context.Context, taskID, executeID, projectID string,
		) ([]*UiCaseExecutionStep, error)
		DeleteRecordByTaskID(ctx context.Context, session sqlx.Session, taskID string) error
	}

	customUiCaseExecutionStepModel struct {
		*defaultUiCaseExecutionStepModel

		conn sqlx.SqlConn
	}
)

// NewUiCaseExecutionStepModel returns a model for the database table.
func NewUiCaseExecutionStepModel(conn sqlx.SqlConn) UiCaseExecutionStepModel {
	return &customUiCaseExecutionStepModel{
		defaultUiCaseExecutionStepModel: newUiCaseExecutionStepModel(conn),
		conn:                            conn,
	}
}

func (m *customUiCaseExecutionStepModel) withSession(session sqlx.Session) UiCaseExecutionStepModel {
	return NewUiCaseExecutionStepModel(sqlx.NewSqlConnFromSession(session))
}

func (m *customUiCaseExecutionStepModel) Table() string {
	return m.table
}

func (m *customUiCaseExecutionStepModel) Fields() []string {
	return uiCaseExecutionStepFieldNames
}

func (m *customUiCaseExecutionStepModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.conn.TransactCtx(ctx, fn)
}

func (m *customUiCaseExecutionStepModel) InsertBuilder(data *UiCaseExecutionStep) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(uiCaseExecutionStepInsertFields...).Values(
		data.TaskId, data.ExecuteId, data.ProjectId, data.CaseId, data.StepId, data.Stage, data.Index,
		data.Name, data.Status, data.StartedAt, data.EndedAt, data.Deleted, data.CreatedBy, data.UpdatedBy,
	)
}

func (m *customUiCaseExecutionStepModel) UpdateBuilder(data *UiCaseExecutionStep) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`stage`":      data.Stage,
		"`index`":      data.Index,
		"`name`":       data.Name,
		"`status`":     data.Status,
		"`started_at`": data.StartedAt,
		"`ended_at`":   data.EndedAt,
		"`deleted`":    data.Deleted,
		"`updated_by`": data.UpdatedBy,
		"`deleted_by`": data.DeletedBy,
		"`deleted_at`": data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customUiCaseExecutionStepModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(uiCaseExecutionStepFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customUiCaseExecutionStepModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customUiCaseExecutionStepModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (
	int64, error,
) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.conn.QueryRowCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customUiCaseExecutionStepModel) FindNoCacheByQuery(
	ctx context.Context, selectBuilder squirrel.SelectBuilder,
) ([]*UiCaseExecutionStep, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*UiCaseExecutionStep
	err = m.conn.QueryRowsCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customUiCaseExecutionStepModel) FindByTaskIDExecuteIDProjectID(
	ctx context.Context, taskID, executeID, projectID string,
) ([]*UiCaseExecutionStep, error) {
	sb := m.SelectBuilder().Where(
		"`task_id` = ? AND `execute_id` = ? AND `project_id` = ?", taskID, executeID, projectID,
	).OrderBy("`stage` ASC", "`index` ASC")
	return m.FindNoCacheByQuery(ctx, sb)
}

func (m *customUiCaseExecutionStepModel) DeleteRecordByTaskID(
	ctx context.Context, session sqlx.Session, taskID string,
) error {
	stmt, values, err := squirrel.Delete(m.table).
		Where("`task_id` = ?", taskID).
		ToSql()
	if err != nil {
		return err
	}

	if session != nil {
		_, err = session.ExecCtx(ctx, stmt, values...)
		return err
	}

	_, err = m.conn.ExecCtx(ctx, stmt, values...)
	return err
}
